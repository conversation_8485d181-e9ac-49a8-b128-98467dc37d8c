import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import { endpoints, epicFetcher } from "./axios";

export function useGetPublicCourtLocations({ page, limit }: { page?: number; limit?: number } = { page: 1, limit: 10 }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getAllPublicCourtLocations}?page=${page}&limit=${limit}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicCourtLocations = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicCourtLocationList: queueData,
            publicCourtLocationListLoading: isLoading,
            publicCourtLocationListError: error,
            publicCourtLocationListValidating: isValidating,
            publicCourtLocationListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicCourtLocations,
    };
}


export function useGetPublicCourtList({ page, limit, courtLocationId }: { page: number; limit: number, courtLocationId?: number }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getAllPublicCourtList}?page=${page}&limit=${limit}&court_location_id=${courtLocationId}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicCourtList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicCourtList: queueData,
            publicCourtListLoading: isLoading,
            publicCourtListError: error,
            publicCourtListValidating: isValidating,
            publicCourtListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicCourtList,
    };
}


export function useGetPublicCourtById({ courtId }: { courtId: number | null }) {

    const fullUrl = useMemo(
        () => courtId ? `${endpoints.booking.getPublicCourtById}/${courtId}` : null,
        [courtId]
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicCourtDetails = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || {};
        return {
            publicCourtDetails: queueData,
            publicCourtDetailsLoading: isLoading,
            publicCourtDetailsError: error,
            publicCourtDetailsValidating: isValidating,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicCourtDetails,
    };
}